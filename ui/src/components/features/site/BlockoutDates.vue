<script lang="ts" setup>
import { computed, onMounted, ref, useTemplateRef, watch } from 'vue'
import DatePicker from 'primevue/datepicker'
import Dialog from 'primevue/dialog'
import InputText from 'primevue/inputtext'
import Textarea from 'primevue/textarea'
import Select from 'primevue/select'
import { format, isAfter, isBefore, isEqual, parseISO } from 'date-fns'
import { notification } from '@/utils/notifications.ts'
import { cloneDeep, isEqual as hasChange } from 'lodash-es'

export interface BlockoutDate {
  id: string
  type: 'single' | 'range' | 'recurring'
  startDate: string
  endDate?: string
  title: string
  reason?: string
  isActive: boolean
  recurringPattern?: {
    frequency: 'daily' | 'weekly' | 'monthly' | 'yearly'
    interval: number
    daysOfWeek?: number[]
    dayOfMonth?: number
    weekOfMonth?: number
    dayOfWeekMonthly?: number
    monthOfYear?: number
    endType?: 'never' | 'on' | 'after'
    occurrences?: number
  }
}

interface Props {
  modelValue?: BlockoutDate[]
  buttonLabel?: string
  minDate?: Date
  maxDate?: Date
  viewOnly?: boolean
}

interface Emits {
  (e: 'update:modelValue', value: BlockoutDate[]): void

  (e: 'save', value: BlockoutDate[]): void

  (e: 'close'): void
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: () => [],
  buttonLabel: 'Set Blockout Dates',
  showDebug: false,
  minDate: undefined,
  maxDate: undefined,
})

const emit = defineEmits<Emits>()

const dialogVisible = ref(false)
const originalDates = ref<BlockoutDate[]>([])
const blockoutDates = ref<BlockoutDate[]>([])

const showAddForm = ref(false)
const newBlockout = ref<Partial<BlockoutDate>>({
  type: 'single',
  title: '',
  reason: '',
  isActive: true,
  recurringPattern: {
    frequency: 'weekly',
    interval: 1,
    daysOfWeek: [1],
    dayOfMonth: 1,
    weekOfMonth: 1,
    dayOfWeekMonthly: 1,
    monthOfYear: 0,
    endType: 'never',
    occurrences: 10,
  },
})
const selectedStartDate = ref<Date | null>(null)
const selectedEndDate = ref<Date | null>(null)

const toggleRef = useTemplateRef<any>('toggleRef')

const blockoutTypes = [
  { label: 'Single Date', value: 'single' },
  { label: 'Date Range', value: 'range' },
]

// const daysOfWeek = [
//   { label: 'Sunday', value: 0 },
//   { label: 'Monday', value: 1 },
//   { label: 'Tuesday', value: 2 },
//   { label: 'Wednesday', value: 3 },
//   { label: 'Thursday', value: 4 },
//   { label: 'Friday', value: 5 },
//   { label: 'Saturday', value: 6 },
// ]
//
// const weeksOfMonth = [
//   { label: 'First', value: 1 },
//   { label: 'Second', value: 2 },
//   { label: 'Third', value: 3 },
//   { label: 'Fourth', value: 4 },
//   { label: 'Last', value: 5 },
// ]
//
// const endTypeOptions = [
//   { label: 'Never', value: 'never' },
//   { label: 'On Date', value: 'on' },
//   { label: 'After Occurrences', value: 'after' },
// ]

watch(
  () => props.modelValue,
  (nv) => {
    blockoutDates.value = cloneDeep(ensureArray(nv))
  },
  { immediate: true, deep: true },
)

watch(
  () => selectedStartDate.value,
  (newDate) => {
    const type = newBlockout.value?.type

    if (type === 'single') {
      selectedEndDate.value = null
    } else if (type === 'range') {
      if (!newDate) {
        selectedEndDate.value = null
        return
      }

      const endDate = selectedEndDate.value

      if (!endDate || !isBefore(newDate, endDate)) {
        const nextDay = new Date(newDate)
        nextDay.setDate(nextDay.getDate() + 1)
        selectedEndDate.value = nextDay
      }
    }
  },
)

onMounted(() => {
  blockoutDates.value = cloneDeep(ensureArray(props.modelValue))
})

const hasChanges = computed(() => {
  return hasChange(originalDates.value, blockoutDates.value)
})

function ensureArray(value: any): BlockoutDate[] {
  if (Array.isArray(value)) return value
  if (value == null) return []
  if (typeof value === 'object' && (value as any).data) {
    return Array.isArray((value as any).data) ? (value as any).data : []
  }
  return []
}

const activeBlockoutDates = computed(() => {
  return ensureArray(blockoutDates.value).filter(function (date) {
    return Boolean(date && date.isActive)
  })
})

const upcomingBlockouts = computed(() => {
  const today = new Date()
  return activeBlockoutDates.value
    .filter((blockout) => {
      if (!blockout.startDate) return false
      try {
        const start = parseISO(blockout.startDate)
        return isAfter(start, today) || isEqual(start, today)
      } catch {
        return false
      }
    })
    .slice(0, 3)
    .map((b) => {
      return {
        ...b,
        displayText: formatBlockoutDisplay(b),
      }
    })
})

function generateId(): string {
  return Date.now().toString(36) + Math.random().toString(36).substring(2)
}

// US Federal Holidays calculation functions
function getNewYearsDay(year: number): Date {
  return new Date(year, 0, 1) // January 1st
}

function getMLKDay(year: number): Date {
  // Third Monday in January
  const firstMonday = new Date(year, 0, 1)
  while (firstMonday.getDay() !== 1) {
    firstMonday.setDate(firstMonday.getDate() + 1)
  }
  return new Date(firstMonday.getTime() + 14 * 24 * 60 * 60 * 1000) // Add 14 days for third Monday
}

function getPresidentsDay(year: number): Date {
  // Third Monday in February
  const firstMonday = new Date(year, 1, 1)
  while (firstMonday.getDay() !== 1) {
    firstMonday.setDate(firstMonday.getDate() + 1)
  }
  return new Date(firstMonday.getTime() + 14 * 24 * 60 * 60 * 1000) // Add 14 days for third Monday
}

function getMemorialDay(year: number): Date {
  // Last Monday in May
  const lastDay = new Date(year, 5, 0) // Last day of May
  while (lastDay.getDay() !== 1) {
    lastDay.setDate(lastDay.getDate() - 1)
  }
  return lastDay
}

function getLaborDay(year: number): Date {
  // First Monday in September
  const firstMonday = new Date(year, 8, 1)
  while (firstMonday.getDay() !== 1) {
    firstMonday.setDate(firstMonday.getDate() + 1)
  }
  return firstMonday
}

function getColumbusDay(year: number): Date {
  // Second Monday in October
  const firstMonday = new Date(year, 9, 1)
  while (firstMonday.getDay() !== 1) {
    firstMonday.setDate(firstMonday.getDate() + 1)
  }
  return new Date(firstMonday.getTime() + 7 * 24 * 60 * 60 * 1000) // Add 7 days for second Monday
}

function getThanksgiving(year: number): Date {
  // Fourth Thursday in November
  const firstThursday = new Date(year, 10, 1)
  while (firstThursday.getDay() !== 4) {
    firstThursday.setDate(firstThursday.getDate() + 1)
  }
  return new Date(firstThursday.getTime() + 21 * 24 * 60 * 60 * 1000) // Add 21 days for fourth Thursday
}

function getUSLegalHolidays(year: number): Array<{ date: Date; title: string }> {
  return [
    { date: getNewYearsDay(year), title: "New Year's Day" },
    { date: getMLKDay(year), title: 'Martin Luther King Jr. Day' },
    { date: getPresidentsDay(year), title: "Presidents' Day" },
    { date: getMemorialDay(year), title: 'Memorial Day' },
    { date: new Date(year, 5, 19), title: 'Juneteenth' }, // June 19th
    { date: new Date(year, 6, 4), title: 'Independence Day' }, // July 4th
    { date: getLaborDay(year), title: 'Labor Day' },
    { date: getColumbusDay(year), title: 'Columbus Day' },
    { date: new Date(year, 10, 11), title: 'Veterans Day' }, // November 11th
    { date: getThanksgiving(year), title: 'Thanksgiving Day' },
    { date: new Date(year, 11, 25), title: 'Christmas Day' }, // December 25th
  ]
}

// function getRecurringDates(blockout: BlockoutDate, maxOccurrences = 100): Date[] {
//   if (blockout.type !== 'recurring' || !blockout.recurringPattern) return []
//   const p = blockout.recurringPattern
//   const start = parseISO(blockout.startDate)
//   const end = blockout.endDate ? parseISO(blockout.endDate) : null
//   const result: Date[] = []
//   let count = 0
//   while (count < (p.endType === 'after' && p.occurrences ? p.occurrences : maxOccurrences)) {
//     let date: Date
//     if (count === 0) {
//       date = new Date(start)
//     } else {
//       switch (p.frequency) {
//         case 'daily':
//           date = addDays(start, p.interval * count)
//           break
//         case 'weekly':
//           date = addWeeks(start, p.interval * count)
//           break
//         case 'monthly':
//           date = addMonths(start, p.interval * count)
//           break
//         case 'yearly':
//           date = addYears(start, p.interval * count)
//           break
//       }
//     }
//     if (p.endType === 'on' && end && isAfter(date, end)) break
//     result.push(date)
//     count++
//   }
//   return result
// }

function formatBlockoutDisplay(blockout: BlockoutDate): string {
  try {
    const s = parseISO(blockout.startDate)
    if (blockout.type === 'single') {
      return format(s, 'MMM dd, yyyy')
    }
    if (blockout.type === 'range' && blockout.endDate) {
      const e = parseISO(blockout.endDate)
      return `${format(s, 'MMM dd')} - ${format(e, 'MMM dd, yyyy')}`
    }
    if (blockout.type === 'recurring' && blockout.recurringPattern) {
      return 'Recurring'
    }
    return format(s, 'MMM dd, yyyy')
  } catch {
    return 'Invalid date'
  }
}

function formatDateForStorage(date: Date): string {
  return date.toISOString().split('T')[0]
}

function addBlockout(): void {
  if (!selectedStartDate.value || !newBlockout.value.title) {
    const hasTitle = newBlockout.value.title
    const hasStartDate = selectedStartDate.value
    notification('warn', `${!hasTitle ? 'Title is required. ' : ''}${!hasStartDate ? 'Start date is required.' : ''}`)
    return
  }
  const b: BlockoutDate = {
    id: generateId(),
    type: newBlockout.value.type as any,
    startDate: formatDateForStorage(selectedStartDate.value),
    endDate: selectedEndDate.value ? formatDateForStorage(selectedEndDate.value) : undefined,
    title: newBlockout.value.title!,
    reason: newBlockout.value.reason || '',
    isActive: true,
    recurringPattern: newBlockout.value.recurringPattern,
  }
  if (b.type === 'range' && (!b.endDate || isAfter(parseISO(b.startDate), parseISO(b.endDate)))) return
  const arr = ensureArray(blockoutDates.value)
  blockoutDates.value = [...arr, b]
  resetForm()
}

function removeBlockout(id: string): void {
  blockoutDates.value = ensureArray(blockoutDates.value).filter((b) => {
    return b.id !== id
  })
}

function toggleBlockout(id: string): void {
  const arr = ensureArray(blockoutDates.value)
  const item = arr.find((b) => {
    return b.id === id
  })
  if (item) item.isActive = !item.isActive
  blockoutDates.value = [...arr]
}

function resetForm(): void {
  newBlockout.value = {
    type: 'single',
    title: '',
    reason: '',
    isActive: true,
    recurringPattern: newBlockout.value.recurringPattern,
  }
  selectedStartDate.value = null
  selectedEndDate.value = null
  showAddForm.value = false
}

function clearAllBlockouts(): void {
  blockoutDates.value = []
}

function addQuickBlockout(type: string): void {
  const today = new Date()
  if (type === 'holiday') {
    selectedStartDate.value = new Date(today.getFullYear(), 11, 25)
    newBlockout.value = { type: 'single', title: 'Christmas Day', reason: 'Holiday', isActive: true }
  }
  if (type === 'weekend') {
    const sat = new Date(today)
    sat.setDate(today.getDate() + ((6 - today.getDay() + 7) % 7))
    const sun = new Date(sat)
    sun.setDate(sat.getDate() + 1)
    selectedStartDate.value = sat
    selectedEndDate.value = sun
    newBlockout.value = { type: 'range', title: 'Weekend', reason: '', isActive: true }
  }
  if (type === 'maintenance') {
    const n = new Date(today)
    n.setDate(today.getDate() + 7)
    selectedStartDate.value = n
    newBlockout.value = { type: 'single', title: 'Maintenance', reason: '', isActive: true }
  }
  if (type === 'legal-holidays') {
    addAllUSLegalHolidays()
    return
  }
  showAddForm.value = true
}

function addAllUSLegalHolidays(): void {
  const currentYear = new Date().getFullYear()
  const holidays = getUSLegalHolidays(currentYear)
  const existingDates = ensureArray(blockoutDates.value)

  let addedCount = 0

  holidays.forEach(({ date, title }) => {
    const dateStr = formatDateForStorage(date)

    // Check if this holiday already exists
    const exists = existingDates.some((existing) => existing.startDate === dateStr && existing.title === title)

    if (!exists) {
      const holiday: BlockoutDate = {
        id: generateId(),
        type: 'single',
        startDate: dateStr,
        title: title,
        reason: 'US Federal Holiday',
        isActive: true,
      }
      existingDates.push(holiday)
      addedCount++
    }
  })

  blockoutDates.value = [...existingDates]

  if (addedCount > 0) {
    notification('success', `Added ${addedCount} US federal holidays for ${currentYear}`)
  } else {
    notification('info', 'All US federal holidays are already added')
  }
}

function openDialog(): void {
  const arr = ensureArray(props.modelValue)
  originalDates.value = cloneDeep(arr)
  blockoutDates.value = cloneDeep(arr)
  dialogVisible.value = true
}

function saveAndClose(): void {
  const arr = ensureArray(blockoutDates.value)
  emit('update:modelValue', arr)
  emit('save', arr)
  dialogVisible.value = false
}

function cancelChanges(): void {
  blockoutDates.value = cloneDeep(originalDates.value)
  resetForm()
  dialogVisible.value = false
}

function onBlockoutTypeChange(): void {
  if (newBlockout.value.type === 'single') {
    selectedEndDate.value = null
  } else if (newBlockout.value.type === 'range') {
    if (!selectedStartDate.value) {
      selectedStartDate.value = new Date()
    }
    if (!selectedEndDate.value) {
      selectedEndDate.value = new Date(selectedStartDate.value)
      selectedEndDate.value.setDate(selectedEndDate.value.getDate() + 1)
    }
  }
}

defineExpose({
  focus: () => {
    if (toggleRef.value) {
      toggleRef.value.$el.focus()
    }
  },
})
</script>

<template>
  <div>
    <MainButton
      ref="toggleRef"
      :pt="{
        root: 'p-0 m-0 !max-h-[40px] truncate w-full ml-1',
        label: 'truncate w-full ',
      }"
      class="truncate p-0"
      severity="secondary"
      size="small"
      text
      @click="openDialog"
    >
      <p
        :class="{
          'placeholder-color': blockoutDates.length === 0 && !props.viewOnly,
        }"
        class="w-full truncate max-w-xs text-sm text-left"
      >
        {{
          props.viewOnly ? upcomingBlockouts.map((b) => b.displayText).join(', ') || 'Blockout Dates' : upcomingBlockouts.map((b) => b.displayText).join(', ') || props.buttonLabel
        }}
      </p>
    </MainButton>

    <!-- Blockout Dates Dialog -->
    <Dialog
      v-model:visible="dialogVisible"
      v-focustrap
      :base-z-index="40000"
      :closable="true"
      :content-style="{ padding: '0' }"
      :draggable="false"
      :header="props.viewOnly ? 'View Blockout Dates' : 'Manager Blockout Dates'"
      :modal="true"
      class="w-full max-w-7xl"
      @after-hide="emit('close')"
    >
      <div class="gap-6 p-6">
        <!-- DatePicker View -->

        <!-- Management Panel -->
        <div class="space-y-4 grid grid-cols-1 lg:grid-cols-2 gap-6">
          <!-- Add New Blockout -->
          <div v-if="!props.viewOnly" class="bg-slate-50 rounded-lg p-4 h-fit">
            <div class="flex items-center justify-between">
              <h5 class="font-medium text-slate-700 mb-2">Add Blockout Date</h5>
              <MainButton v-if="!showAddForm" :disabled="props.viewOnly" icon="pi pi-plus" severity="success" size="small" @click="showAddForm = true" />
            </div>

            <div v-if="showAddForm" class="space-y-3">
              <div class="grid grid-cols-2 gap-3">
                <div>
                  <label class="block text-sm font-medium text-slate-600 mb-1">Type</label>
                  <Select
                    v-model="newBlockout.type"
                    :disabled="props.viewOnly"
                    :options="blockoutTypes"
                    class="w-full"
                    option-label="label"
                    option-value="value"
                    placeholder="Select type"
                    @change="onBlockoutTypeChange"
                  />
                </div>
                <div>
                  <label class="block text-sm font-medium text-slate-600 mb-1">Title</label>
                  <InputText v-model="newBlockout.title" :disabled="props.viewOnly" class="w-full" placeholder="e.g., Holiday Closure" />
                </div>
              </div>

              <div class="grid grid-cols-2 gap-3">
                <div>
                  <label class="block text-sm font-medium text-slate-600 mb-1">Start Date</label>
                  <DatePicker v-model="selectedStartDate" class="w-full" date-format="mm/dd/yy" placeholder="Select start date" show-icon />
                </div>
                <div v-if="newBlockout.type === 'range'">
                  <label class="block text-sm font-medium text-slate-600 mb-1">End Date</label>
                  <DatePicker
                    v-if="selectedStartDate"
                    v-model="selectedEndDate"
                    :disabled="props.viewOnly"
                    :min-date="selectedStartDate"
                    class="w-full"
                    date-format="mm/dd/yy"
                    placeholder="Select end date"
                    show-icon
                  />
                </div>
              </div>

              <div>
                <label class="block text-sm font-medium text-slate-600 mb-1">Reason (Optional)</label>
                <Textarea v-model="newBlockout.reason" :disabled="props.viewOnly" class="w-full" placeholder="Brief description of why this date is blocked" rows="2" />
              </div>

              <div class="flex justify-end space-x-2">
                <MainButton :disabled="props.viewOnly" label="Cancel" severity="secondary" size="small" variant="outlined" @click="resetForm" />
                <MainButton :disabled="props.viewOnly" label="Add Blockout" size="small" @click="addBlockout" />
              </div>
            </div>
          </div>

          <!-- Existing Blockouts List -->
          <div class="bg-white border border-slate-200 rounded-lg">
            <div class="flex items-center justify-between p-4 border-b border-slate-200">
              <h5 class="font-medium text-slate-700">Blockout Dates ({{ ensureArray(blockoutDates).length }})</h5>
              <MainButton
                v-if="ensureArray(blockoutDates).length > 0 && !props.viewOnly"
                :disabled="props.viewOnly"
                label="Clear All"
                severity="danger"
                size="small"
                variant="outlined"
                @click="clearAllBlockouts"
              />
            </div>

            <div v-if="ensureArray(blockoutDates).length === 0" class="p-6 text-center text-slate-500">
              <i class="pi pi-calendar-times text-2xl mb-2 block"></i>
              No blockout dates configured
            </div>

            <div
              v-else
              :class="{
                'max-h-96': props.viewOnly,
              }"
              class="max-h-64 overflow-y-auto"
            >
              <div
                v-for="blockout in ensureArray(blockoutDates)"
                :key="blockout.id"
                :class="{ 'opacity-50': !blockout.isActive }"
                class="flex items-center justify-between p-3 border-b border-slate-100 last:border-b-0"
              >
                <div class="flex-1">
                  <div class="font-medium text-slate-800">{{ blockout.title }}</div>
                  <div class="text-sm text-slate-600">{{ formatBlockoutDisplay(blockout) }}</div>
                  <div v-if="blockout.reason" class="text-xs text-slate-500 mt-1">
                    {{ blockout.reason }}
                  </div>
                </div>
                <div v-if="!props.viewOnly" class="flex items-center space-x-2">
                  <MainButton
                    :disabled="props.viewOnly"
                    :icon="blockout.isActive ? 'pi pi-eye-slash' : 'pi pi-eye'"
                    :severity="blockout.isActive ? 'warning' : 'success'"
                    size="small"
                    variant="outlined"
                    @click="toggleBlockout(blockout.id)"
                  />
                  <MainButton :disabled="props.viewOnly" icon="pi pi-trash" severity="danger" size="small" variant="outlined" @click="removeBlockout(blockout.id)" />
                </div>
              </div>
            </div>
          </div>
        </div>

        <div v-if="!props.viewOnly" class="space-y-4 col-span-full">
          <!-- Quick Actions -->
          <div v-if="!props.viewOnly" class="space-y-2">
            <h4 class="font-medium text-slate-600 text-sm">Quick Add:</h4>
            <div class="flex flex-wrap gap-2">
              <MainButton :disabled="props.viewOnly" label="Holiday" severity="secondary" size="small" variant="outlined" @click="addQuickBlockout('holiday')" />
              <MainButton :disabled="props.viewOnly" label="Weekend" severity="secondary" size="small" variant="outlined" @click="addQuickBlockout('weekend')" />
              <MainButton :disabled="props.viewOnly" label="Maintenance" severity="secondary" size="small" variant="outlined" @click="addQuickBlockout('maintenance')" />
              <MainButton :disabled="props.viewOnly" label="Legal Holidays" severity="info" size="small" variant="outlined" @click="addQuickBlockout('legal-holidays')" />
            </div>
          </div>
        </div>
      </div>

      <template v-if="!props.viewOnly" #footer>
        <MainButton :disabled="props.viewOnly" label="Cancel" severity="secondary" size="small" variant="outlined" @click="cancelChanges" />
        <MainButton :disabled="props.viewOnly || hasChanges" label="Save Changes" size="small" @click="saveAndClose" />
      </template>
    </Dialog>
  </div>
</template>

<style lang="scss">
@use '@/assets/mixins' as *;

@include custom-scrollbar();
</style>
