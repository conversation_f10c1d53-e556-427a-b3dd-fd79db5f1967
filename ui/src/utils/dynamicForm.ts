// only used in the dynamic form components

// external imports
import { cloneDeep, isEmpty, startCase, upperFirst } from 'lodash-es'
import type { FormItemRule } from 'element-plus'

// internal imports
import type {
  BuildFormFieldsParams,
  FieldsConfig,
  FormField,
  Layouts,
  ModelName,
  Rules
} from '@/components/general/form/types.ts'
import type { FieldModel } from './types'
import { useModelStore } from '@/stores/model.store'
import { GetMany } from './queryMethods'
import apolloClient from '@/services/apollo.ts'
import { provideApolloClient, useLazyQuery } from '@vue/apollo-composable'
import { notification } from '@/utils/notifications.ts'

import {
  DateFieldTypes,
  FieldRelationType,
  FieldType,
  ManyRelationFieldTypes,
  NumericFieldTypes,
  OneRelationFieldTypes
} from './constants/fieldTypes'
import type { Maybe, Scalars } from '@/gql/graphql'

const defaultRelationsToShow = [FieldRelationType.Scalar, FieldRelationType.Component, ...ManyRelationFieldTypes, ...OneRelationFieldTypes]

function getModelFormFields<T>({ modelName, fields, fieldsConfig, recordId = undefined }: BuildFormFieldsParams<T>): {
  fields: FormField[]
  rules: Rules
} {
  const modelStore = useModelStore()

  const model = modelStore.getModel(modelName)
  if (!model) throw new Error(`Model ${modelName} not found`)

  const modelFields = model.fields.filter((f) => defaultRelationsToShow.includes(f.relationType) && fields.includes(f.name))

  const formRules: Rules = {}
  const formFields = modelFields
    .map((f) => {
      const { field, rules } = buildFormField(f, modelName, recordId, fieldsConfig)
      formRules[f.name] = rules
      return field
    })
    .sort((a, b) => fields.indexOf(a.name) - fields.indexOf(b.name))

  return {
    fields: formFields,
    rules: formRules,
  }
}

function buildFormField<T>(
  field: FieldModel,
  modelName: ModelName,
  recordId: string | undefined,
  fieldsConfig?: FieldsConfig<T>,
): {
  field: FormField
  rules: FormItemRule[]
} {
  const rules: FormItemRule[] = []

  const fieldsConfigKey = field.name as keyof T

  const formField: FormField = {
    name: field.name,
    relationType: field.relationType,
    type: field.type,
    target: field.target,
    enumValues: field.enumValues?.filter((val: string) => !fieldsConfig?.[fieldsConfigKey]?.excludedValues?.includes(val)),
    unique: field.unique,
    label: fieldsConfig?.[fieldsConfigKey]?.label,
    placeholder: fieldsConfig?.[fieldsConfigKey]?.placeholder || '',
    remoteOptions: fieldsConfig?.[fieldsConfigKey]?.remoteOptions || undefined,
    addressOptions: fieldsConfig?.[fieldsConfigKey]?.addressOptions || undefined,
    datePickerOptions: fieldsConfig?.[fieldsConfigKey]?.datePickerOptions || undefined,
    icon: fieldsConfig?.[fieldsConfigKey]?.icon || undefined,
    visibleMethod: fieldsConfig?.[fieldsConfigKey]?.visibleMethod || undefined,
    disabled: fieldsConfig?.[fieldsConfigKey]?.disabled || false,
  }

  // Add custom rules
  if (fieldsConfig?.[fieldsConfigKey]?.rules) {
    rules.push(...(fieldsConfig[fieldsConfigKey].rules as FormItemRule[]))
  }

  // Add unique validation rule
  if (field.unique) {
    rules.push(getUniqueValidationRule(modelName, field, recordId))
  }

  // Add email validation rule
  if (field.type === FieldType.Email) {
    rules.push({
      type: 'email',
      message: `${upperFirst(field.name)} is not a valid email address`,
    })
  }

  // Add required validation rule
  if (field.required) {
    rules.push({
      trigger: 'change',
      required: true,
      message: `${upperFirst(field.name)} is required`,
    })
  }

  return { field: formField, rules }
}

function getInitialFormData(data: Record<string, any>, fields: FormField[], slotFields: string[]): Record<string, any> {
  const initialData: Record<string, any> = {}

  for (const field of fields) {
    if (OneRelationFieldTypes.includes(field.relationType)) {
      initialData[field.name] = data[field.name] ? data[field.name]?.documentId : null
    } else if (ManyRelationFieldTypes.includes(field.relationType)) {
      initialData[field.name] = data[field.name]?.map((d: { documentId: string }) => d.documentId) || []
    } else if (field.type === FieldType.Boolean) {
      initialData[field.name] = data[field.name] || false
    } else if (NumericFieldTypes.includes(field.type)) {
      initialData[field.name] = data[field.name] || null
    } else if (DateFieldTypes.includes(field.type)) {
      initialData[field.name] = data[field.name] || null
    } else if (field.type === FieldType.Enumeration) {
      initialData[field.name] = data[field.name] || null
    } else {
      initialData[field.name] = data[field.name] || ''
    }
  }
  // handle fields that are in slots
  for (const f of slotFields) {
    const isInFields = fields.find((field) => field.name === f)
    if (isInFields) continue
    initialData[f] = data[f] || ''
  }

  return cloneDeep(initialData)
}

function getFormClasses(layout: Layouts, columns: number): string {
  const columnsLimited = Math.min(columns, 12)

  const smCols = 1
  const mdCols = Math.min(2, columnsLimited)
  const lgCols = Math.min(3, columnsLimited)
  const xlCols = columnsLimited
  const gapClasses = 'gap-x-6 gap-y-0'

  const classes: Record<Layouts, string> = {
    default: 'px-6',
    grid: `px-6 grid sm:grid-cols-${smCols} md:grid-cols-${mdCols} lg:grid-cols-${lgCols} xl:grid-cols-${xlCols} ${gapClasses}`,
    inline: `grid sm:grid-cols-${smCols} md:grid-cols-${mdCols} lg:grid-cols-${lgCols} xl:grid-cols-${xlCols}  gap-x-4 gap-y-0`,
    group: 'grid grid-cols-12 gap-x-4 gap-y-2',
    'group-modal': '!px-6 grid grid-cols-12 gap-x-10 gap-y-2',
  }

  return classes[layout] || ''
}

function getFieldClasses(layout: Layouts, columns: number): string {
  const columnsLimited = Math.min(columns, 12)

  const smColSpan = 'col-span-full'
  const mdColSpan = columnsLimited >= 2 ? 'col-span-1' : 'col-span-full'
  const lgColSpan = columnsLimited >= 3 ? 'col-span-1' : 'col-span-full'
  const xlColSpan = 'col-span-1'

  const classes: Record<Layouts, string> = {
    default: '',
    grid: `sm:${smColSpan} md:${mdColSpan} lg:${lgColSpan} xl:${xlColSpan}`,
    inline: 'col-span-full md:col-span-1',
    group: 'col-span-full md:col-span-1',
    'group-modal': 'col-span-full md:col-span-1',
  }

  return classes[layout] || ''
}

function setFormFieldsColor(layout: Layouts, formEl: Element, add = false): void {
  const isGroupOrInlineLayout = ['group', 'inline'].includes(layout)
  if (!isGroupOrInlineLayout) return
  const elements = formEl?.getElementsByClassName('el-form-item__content')
  if (!elements) return
  for (let i = 0; i < elements.length; i++) {
    // TODO: can we use a better way to set the color?
    // we need to target that class only when editMode is active not all the time
    !add ? elements[i].removeAttribute('style') : elements[i].setAttribute('style', 'background-color: #FBFBFB;')
  }
}

function isFile(value: any): boolean {
  return value instanceof File
}

/**
 * Generates a validation rule to ensure the uniqueness of a field's value within a specified database model,
 *
 * @param model - The name of the model to query in the database.
 * @param field - An object representing the field to validate. Use the `getField` method from the model store
 *                to retrieve the field object
 * @param recordId - (Optional) The ID of the record to exclude from the uniqueness check. ('typically used in updates`)
 * @param additionalFilters - (Optional) Additional filters to apply to the uniqueness check.
 * @param message - (Optional) A custom error message to display when the field value is not unique.
 * @returns A `FormItemRule` object with asynchronous validation logic for checking uniqueness.
 *
 */
function getUniqueValidationRule(model: ModelName, field: FieldModel, recordId: Maybe<Scalars['ID']['output']> = '', additionalFilters: any = {}, message?: string): FormItemRule {
  return {
    asyncValidator: async (rule, value) => {
      try {
        if (!value || (model === 'UploadFile' && field.name === 'name')) return Promise.resolve()
        const query = GetMany(model, ['documentId'])
        const operationName = useModelStore().getManyOperation(model)
        console.log('operationName', operationName)
        provideApolloClient(apolloClient)

        const sanitizedValue = ['text', 'string'].includes(field.type) ? (!isFile(value) ? value.replace(/\s+/g, ' ').trim() : value.name.replace(/\s+/g, ' ').trim()) : value

        const filters: Record<string, any> = {
          and: [
            {
              [field.name]: { eqi: sanitizedValue },
            },
          ],
        }

        if (recordId) {
          filters.and.push({
            documentId: { ne: recordId },
          })
        }

        if (!isEmpty(additionalFilters)) {
          filters.and.push(additionalFilters)
        }

        const { load } = useLazyQuery(
          query,
          {
            filters,
            pagination: { limit: 1 },
          },
          { fetchPolicy: 'network-only' },
        )
        const response = await load()
        if (response && response[operationName]?.pageInfo?.total > 0) {
          return message ? Promise.reject(message) : Promise.reject(`${model} with field ${startCase(field.name)} already exists`)
        }

        return Promise.resolve()
      } catch (error) {
        const err = error as Error
        notification('error', err?.message)
        return Promise.reject(err?.message)
      }
    },
    trigger: 'blur',
  }
}

export function isOutsideClick(event: PointerEvent): boolean {
  const target = event.target as HTMLElement
  const parentRoleIsOption = target.parentNode && (target.parentNode as HTMLElement).role === 'option'
  const isSvg = target.tagName === 'svg'
  const role = target.role

  console.log('isOutsideClick', event)
  // check if element has id "loadmore"
  if (target.id === 'loadmore') {
    return false
  }

  let isElementOutSide: boolean

  if (parentRoleIsOption || isSvg || role === 'option' || role === 'loadmore') {
    isElementOutSide = false
  } else if (role === null) {
    isElementOutSide = true
  } else {
    isElementOutSide = role !== 'contentinfo'
  }

  return isElementOutSide
}

export { getModelFormFields, getInitialFormData, getFormClasses, getFieldClasses, setFormFieldsColor, getUniqueValidationRule }
